package com.wexl.erp.integration.teacher.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;

public record StudentDto() {

  @Builder
  public record StudentRequest(String key, @JsonProperty("academic_year") Integer academicYear) {}

  @Builder
  public record StudentResponse(String status, int count, List<Student> data) {}

  @Builder
  public record Student(
      @JsonProperty("first_name") String firstName,
      @JsonProperty("middle_name") String middleName,
      @JsonProperty("last_name") String lastName,
      @JsonProperty("full_name") String fullName,
      @JsonProperty("student_mobile") String studentMobile,
      @JsonProperty("student_email_id") String studentEmailId,
      @JsonProperty("father_name") String fatherName,
      @JsonProperty("mother_name") String motherName,
      @JsonProperty("father_mobile") String fatherMobile,
      @JsonProperty("mother_mobile") String motherMobile,
      @JsonProperty("father_email") String fatherEmail,
      @JsonProperty("mother_email") String motherEmail,
      @JsonProperty("father_occupation") String fatherOccupation,
      @JsonProperty("mother_occupation") String motherOccupation,
      @JsonProperty("student_admission_no") String studentAdmissionNo,
      @JsonProperty("class_id") String classId,
      @JsonProperty("academic_year") String academicYear,
      @JsonProperty("gender") String gender,
      @JsonProperty("dob") String dob,
      @JsonProperty("class_roll_num") String classRollNum,
      @JsonProperty("date_of_admission") String dateOfAdmission,
      @JsonProperty("class_name") String className,
      @JsonProperty("master_class_id") String masterClassId,
      @JsonProperty("master_class_name") String masterClassName) {}

  @Builder
  public record Fee(
      @JsonProperty("student_name") String studentName,
      @JsonProperty("admission_no") String admissionNo,
      @JsonProperty("roll_no") String rollNo,
      @JsonProperty("class_name") String className,
      @JsonProperty("due_amount") String dueAmount,
      @JsonProperty("dob") String dob) {}

  @Builder
  public record FeeResponse(String status, int count, List<Fee> data) {}
}
