package com.wexl.erp.integration.teacher.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wexl.erp.integration.teacher.config.FeeOrgConfigProperties;
import com.wexl.erp.integration.teacher.dto.*;
import com.wexl.erp.integration.teacher.entity.*;
import com.wexl.erp.integration.teacher.repository.*;
import com.wexl.erp.integration.teacher.util.ErpUtil;
import com.wexl.erp.integration.teacher.util.JsonUtil;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.Timestamp;
import java.time.Duration;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

@Component
@RequiredArgsConstructor
@Slf4j
public class ErpStudentSyncService {
  @Value("${app.erp.student.url}")
  private String erpStudentUrl;

  @Value("${app.stm.student.url}")
  private String stmStudentUrl;

  @Value("${app.teacherSyncUrl}")
  private String studentSyncUrl;

  @Value("${app.erp.teacher.refreshfeed}")
  private boolean refreshFeed;

  private final ErpUtil erpUtil;
  private final RestTemplate restTemplate;
  private final RawErpStudentRepository rawErpStudentRepository;
  private final ErpStudentRepository erpStudentRepository;
  private final ErpJobService erpJobService;
  private final JsonUtil jsonUtil;
  private final ExcelReader excelReader;
  private final RawErpFeeRepository rawErpFeeRepository;
  private final ErpFeeRepository erpFeeRepository;
  private final FeeOrgConfigProperties feeOrgConfigProperties;
  private final ErpStudentSectionChangeRepository sectionChangeRepository;
  private final Map<String, SectionMapper> SectionMappers = new HashMap<>();

  public void syncStmStudents(String orgSlug) {
    var stmJob = erpJobService.createJobEntry("STUDENT", orgSlug);
    List<StudentDto.Student> allStudents = getLatestStudents(orgSlug);
    final Map<String, ErpStudent> studentByCodeMap = getStudentByCodeMap(null, orgSlug);
    process(allStudents, studentByCodeMap, null, stmJob, orgSlug);
    deleteUnknownStudents(allStudents, stmJob, orgSlug);
    pushUpdatesToQueue(stmJob, orgSlug);
    erpJobService.markCompleted(stmJob);
  }

  public void syncStudents(String orgSlug) {
    var erpJob = erpJobService.createJobEntry("STUDENT", orgSlug);
    var schoolId = erpUtil.getSchoolIdForOrgSlug(orgSlug);
    final List<StudentDto.Student> allStudents = getNewStudents(schoolId);
    final Map<String, ErpStudent> studentByCodeMap = getStudentByCodeMap(schoolId, null);
    process(allStudents, studentByCodeMap, schoolId, erpJob, orgSlug);
    deleteUnknownStudents(allStudents, erpJob, orgSlug);
    pushUpdatesToQueue(erpJob, orgSlug);
    log.info("Syncing students completed");
    erpJobService.markCompleted(erpJob);
    deleteSectionChangedStudents();
  }

  private void deleteSectionChangedStudents() {
    sectionChangeRepository.deleteAll();
    log.info("deleted section changes students");
  }

  private List<StudentDto.Student> getLatestStudents(String orgSlug) {
    if (refreshFeed) {
      final List<StudentDto.Student> allStudents = fetchStudentsFromStm();
      log.info("Fetched {} students from ERP", allStudents.size());
      saveRawStudentData(allStudents, null);
      log.info("Saved {} students to raw student repository", allStudents.size());
      return allStudents;
    }
    return transformToStudents(rawErpStudentRepository.findBySchoolId(orgSlug));
  }

  private List<StudentDto.Student> fetchStudentsFromStm() {
    try {
      HttpClient httpClient = HttpClient.newBuilder()
              .connectTimeout(Duration.ofSeconds(30))
              .build();

      HttpRequest request = HttpRequest.newBuilder()
              .uri(URI.create(stmStudentUrl))
              .timeout(Duration.ofSeconds(30))
              .header("Origin", "https://learn.academyteacher.com")
              .header("Content-Type", "application/json")
              .POST(HttpRequest.BodyPublishers.ofString("{}"))
              .build();


      HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

      if (response.statusCode() == 200) {
        ObjectMapper objectMapper = new ObjectMapper();
        return objectMapper.readValue(
                response.body(),
                new TypeReference<List<StudentDto.Student>>() {}
        );
      } else {
        log.error("Failed to fetch students from STM. HTTP Status: {}", response.statusCode());
        return Collections.emptyList();
      }
    } catch (Exception e) {
      log.error("Error fetching students from STM: {}", e.getMessage(), e);
      return Collections.emptyList();
    }
  }

  public void syncStudentFee(String orgSlug) {
    var erpJob = erpJobService.createJobEntry("FEE", orgSlug);
    var schoolId = erpUtil.getSchoolIdForStudentOrgSlug(orgSlug);
    final List<Fee> allStudents = getNewStudentFee(schoolId, orgSlug);
    final Map<String, ErpFee> studentByCodeMap = getFeeStudentByCodeMap(schoolId);
    var dobStudents =
        rawErpFeeRepository.getFeeStudents(
            orgSlug, allStudents.stream().map(Fee::getAdmissionNo).toList());
    feeProcess(dobStudents, studentByCodeMap, schoolId, erpJob, orgSlug);
    deleteUnknownFeeStudents(dobStudents, erpJob, orgSlug);
    pushFeeUpdatesToQueue(orgSlug, erpJob, allStudents.stream().map(Fee::getAdmissionNo).toList());
    log.info("Syncing students completed");
    erpJobService.markCompleted(erpJob);
  }

  private void deleteUnknownStudents(
      List<StudentDto.Student> students, ErpJob erpJob, String orgSlug) {
    log.info("Deleting unknown students now..");
    final List<String> studentCodes = students.stream().map(this::constructStudentCode).toList();
    final List<ErpStudent> erpStudents =
        erpStudentRepository.findAllByStudentCodeNotInAndOrgSlug(studentCodes, orgSlug);
    if (!erpStudents.isEmpty()) {
      erpStudents.forEach(s -> erpJobService.markStudent(null, s, erpJob, "DELETE", false));
      String schoolId = erpStudents.getFirst().getSchoolId();
      erpStudentRepository.deleteAllBySchoolId(schoolId);
      log.info("Deleted {} unknown students for school ID: {}", erpStudents.size(), schoolId);
    } else {
      log.info("No unknown students found to delete");
    }

    erpJobService.saveJob(erpJob);
    log.info("Deleting unknown students completed");
  }

  private void deleteUnknownFeeStudents(List<Fee> students, ErpJob erpJob, String orgSlug) {
    log.info("Deleting unknown students now..");
    final List<String> studentCodes = students.stream().map(this::constructFeeStudentCode).toList();
    final List<ErpFee> erpStudents =
        erpFeeRepository.findAllByStudentCodeNotInAndOrgSlug(studentCodes, orgSlug);
    if (!erpStudents.isEmpty()) {
      erpStudents.forEach(s -> erpJobService.markFeeStudent(null, s, erpJob, "DELETE"));
      String schoolId = erpStudents.getFirst().getSchoolId();
      rawErpFeeRepository.deleteAllBySchoolId(schoolId);
      log.info("Deleted {} unknown students for school ID: {}", erpStudents.size(), schoolId);
    } else {
      log.info("No unknown students found to delete");
    }

    erpJobService.saveJob(erpJob);
    log.info("Deleting unknown students completed");
  }

  private void pushUpdatesToQueue(ErpJob erpJob, String orgSlug) {
    log.info("Pushing student updates to retail service now");
    List<ErpData.ErpEntityChange> erpEntityChangeList =
        erpJob.getJobDetails().stream()
            .map(
                c ->
                    ErpData.ErpEntityChange.builder()
                        .dateTime(String.valueOf(c.getCreatedAt()))
                        .id(c.getId())
                        .employeeCode(c.getRecordId())
                        .changeType(c.getOperation())
                        .studentResponse(toStudentResponse(c))
                        .type("student")
                        .build())
            .toList();

    List<String> changedStudentCodes =
        erpJob.getJobDetails().stream()
            .filter(ErpJobDetail::isSectionChanges)
            .map(ErpJobDetail::getRecordId)
            .distinct()
            .toList();

    List<ErpStudentSectionChange> sectionChangedStudents =
        sectionChangeRepository.findByStudentCodeIn(changedStudentCodes);

    final ErpData.ErpEntityChangeResponse studentSnapshots =
        ErpData.ErpEntityChangeResponse.builder().erpEntityChanges(erpEntityChangeList).build();
    erpUtil.pushToQueue(studentSnapshots, orgSlug, sectionChangedStudents);
    log.info("Pushing updates to retail service completed");
  }

  private void pushFeeUpdatesToQueue(String orgSlug, ErpJob erpJob, List<String> admissionNumbers) {
    log.info("Pushing student updates to retail service now");
    List<ErpData.ErpEntityChange> erpEntityChangeList =
        erpJob.getJobDetails().stream()
            .map(
                c ->
                    ErpData.ErpEntityChange.builder()
                        .dateTime(String.valueOf(c.getCreatedAt()))
                        .id(c.getId())
                        .employeeCode(c.getRecordId())
                        .changeType(c.getOperation())
                        .feeResponse(toFeeStudentResponse(c))
                        .type("fee")
                        .build())
            .toList();
    final ErpData.ErpEntityChangeResponse studentSnapshots =
        ErpData.ErpEntityChangeResponse.builder()
            .erpEntityChanges(erpEntityChangeList)
            .admissionNumbers(admissionNumbers)
            .build();
    erpUtil.pushToQueue(studentSnapshots, orgSlug, Collections.emptyList());
    log.info("Pushing updates to retail service completed");
  }

  private ErpData.ErpStudentResponse toStudentResponse(ErpJobDetail c) {
    final String studentId = c.getRecordId();

    final Optional<ErpStudent> byStudentCodeAndOrgSlug =
        erpStudentRepository.findByStudentCodeAndOrgSlug(studentId, c.getJob().getOrgSlug());
    if (byStudentCodeAndOrgSlug.isEmpty()) {
      return null;
    }
    var erpStudent = byStudentCodeAndOrgSlug.get();
    var middleName = erpStudent.getMiddleName() != null ? " " + erpStudent.getMiddleName() : "";
    return ErpData.ErpStudentResponse.builder()
        .id(erpStudent.getId())
        .studentCode(erpStudent.getStudentCode())
        .firstName(erpStudent.getFirstName() + middleName)
        .lastName(erpStudent.getLastName())
        .studentCode(erpStudent.getStudentCode())
        .gender(erpStudent.getGender())
        .classRollNo(erpStudent.getClassRollNum())
        .fatherName(erpStudent.getFatherName())
        .motherName(erpStudent.getMotherName())
        .fatherEmail(erpStudent.getFatherEmail())
        .motherEmail(erpStudent.getMotherEmail())
        .fatherPhone(erpStudent.getFatherMobile())
        .motherPhone(erpStudent.getMotherMobile())
        .email(erpStudent.getStudentEmailId())
        .phone(erpStudent.getFatherMobile())
        .rollNumber(erpStudent.getStudentAdmissionNo())
        .fatherName(erpStudent.getFatherName())
        .motherName(erpStudent.getMotherName())
        .grade(erpStudent.getGradeSlug())
        .gradeSlug(erpStudent.getGradeSlug())
        .sectionUuid(erpStudent.getSectionUuid())
        .sectionName(erpStudent.getClassName())
        .orgSlug(erpStudent.getOrgSlug())
        .build();
  }

  private ErpData.ErpFeeResponse toFeeStudentResponse(ErpJobDetail c) {
    final String studentId = c.getRecordId();

    final Optional<ErpFee> byStudentCodeAndOrgSlug =
        erpFeeRepository.findByStudentCodeAndOrgSlug(studentId, c.getJob().getOrgSlug());
    if (byStudentCodeAndOrgSlug.isEmpty()) {
      return null;
    }
    var erpStudentFee = byStudentCodeAndOrgSlug.get();
    return ErpData.ErpFeeResponse.builder()
        .studentCode(erpStudentFee.getStudentCode())
        .studentCode(erpStudentFee.getStudentCode())
        .studentName(erpStudentFee.getStudentName())
        .admissionNo(erpStudentFee.getAdmissionNo())
        .rollNo(erpStudentFee.getRollNo())
        .className(erpStudentFee.getClassName())
        .dueAmount(erpStudentFee.getDueAmount())
        .orgSlug(erpStudentFee.getOrgSlug())
        .build();
  }

  private void process(
      List<StudentDto.Student> allStudentsFromFeed,
      Map<String, ErpStudent> studentByCodeMap,
      String schoolId,
      ErpJob erpJob,
      String orgSlug) {
    List<ErpStudent> erpStudents = new ArrayList<>();
    List<ErpStudentSectionChange> sectionChanges = new ArrayList<>();
    for (StudentDto.Student studentFromFeed : allStudentsFromFeed) {
      var studentCode = constructStudentCode(studentFromFeed);
      if (studentByCodeMap.containsKey(studentCode)) {
        final ErpStudent erpStudent = studentByCodeMap.get(studentCode);
        if (isStudentChanged(erpStudent, studentFromFeed)) {
          boolean sectionChanged = isClassChanged(erpStudent, studentFromFeed);
          if (sectionChanged) {
            sectionChanges.add(buildSectionChange(erpStudent, studentFromFeed, orgSlug));
          }
          erpStudents.add(saveStudent(erpStudent, schoolId, studentFromFeed, orgSlug));
          erpJobService.markStudent(studentFromFeed, erpStudent, erpJob, "UPDATE", sectionChanged);
        }
      } else {
        boolean sectionChanged = false;
        erpJobService.markStudent(studentFromFeed, null, erpJob, "ADD", sectionChanged);
        erpStudents.add(saveStudent(new ErpStudent(), schoolId, studentFromFeed, orgSlug));
      }
    }
    if (!sectionChanges.isEmpty()) {
      sectionChangeRepository.saveAll(sectionChanges);
      log.info("Section changed students are saved: {}", sectionChanges.size());
    }
    erpJobService.saveJob(erpJob);
    erpStudentRepository.saveAll(erpStudents);
  }

  private ErpStudentSectionChange buildSectionChange(
      ErpStudent erpStudent, StudentDto.Student studentFromFeed, String orgSlug) {
    return ErpStudentSectionChange.builder()
        .studentCode(erpStudent.getStudentCode())
        .firstName(erpStudent.getFirstName())
        .lastName(erpStudent.getLastName())
        .wexlSection(erpStudent.getClassName())
        .erpSection(studentFromFeed.className())
        .orgSlug(orgSlug)
        .recordedAt(new Timestamp(System.currentTimeMillis()).toLocalDateTime())
        .build();
  }

  private boolean isClassChanged(ErpStudent erpStudent, StudentDto.Student studentFromFeed) {
    return !Objects.equals(erpStudent.getClassName(), studentFromFeed.className());
  }

  private void feeProcess(
      List<Fee> allStudentsFromFeed,
      Map<String, ErpFee> studentByCodeMap,
      String schoolId,
      ErpJob erpJob,
      String orgSlug) {
    List<ErpFee> erpFees = new ArrayList<>();
    for (Fee studentFromFeed : allStudentsFromFeed) {
      var studentCode = constructFeeStudentCode(studentFromFeed);
      if (studentByCodeMap.containsKey(studentCode)) {
        final ErpFee erpFee = studentByCodeMap.get(studentCode);
        if (!isStudentFeeChanged(erpFee, studentFromFeed)) {
          erpFees.add(saveFeeStudent(erpFee, schoolId, studentFromFeed, orgSlug));
          erpJobService.markFeeStudent(studentFromFeed, erpFee, erpJob, "UPDATE");
        }
      } else {
        erpJobService.markFeeStudent(studentFromFeed, null, erpJob, "UPDATE");
        erpFees.add(saveFeeStudent(new ErpFee(), schoolId, studentFromFeed, orgSlug));
      }
    }
    erpJobService.saveJob(erpJob);
    erpFeeRepository.saveAll(erpFees);
  }

  private boolean isStudentChanged(ErpStudent erpStudent, StudentDto.Student studentFromFeed) {
    String[] fieldsToCheck = {
      "firstName",
      "middleName",
      "lastName",
      "studentEmailId",
      "fatherName",
      "fatherMobile",
      "fatherEmail",
      "motherName",
      "motherMobile",
      "motherEmail",
      "studentAdmissionNo",
      "masterClassName",
      "className"
    };

    Map<String, String[]> changedFields = new LinkedHashMap<>();

    for (String field : fieldsToCheck) {
      if (erpUtil.checkFieldsHaveDifferentValue(field, studentFromFeed, erpStudent)) {
        Object oldVal = erpUtil.readField(erpStudent, field);
        Object newVal = erpUtil.readField(studentFromFeed, field);
        changedFields.put(
            field,
            new String[] {
              oldVal != null ? oldVal.toString() : "null",
              newVal != null ? newVal.toString() : "null"
            });
      }
    }

    if (!changedFields.isEmpty()) {
      log.info("Student changes detected for studentId={}", erpStudent.getStudentCode());
      changedFields.forEach(
          (field, values) ->
              log.info("  Field '{}' changed from '{}' to '{}'", field, values[0], values[1]));
      return true;
    }

    return false;
  }

  private boolean isStudentFeeChanged(ErpFee erpFee, Fee studentFromFeed) {
    String[] fieldsToCheck = new String[] {"dueAmount"};
    for (String field : fieldsToCheck) {
      if (erpUtil.checkFieldsHaveDifferentValue(field, studentFromFeed, erpFee)) {
        return true;
      }
    }
    return false;
  }

  private ErpStudent saveStudent(
      ErpStudent erpStudent, String schoolId, StudentDto.Student studentFromFeed, String orgSlug) {
    erpStudent.setFirstName(studentFromFeed.firstName());
    erpStudent.setMiddleName(
        studentFromFeed.middleName().isEmpty() ? "" : studentFromFeed.middleName());
    erpStudent.setLastName(studentFromFeed.lastName());
    erpStudent.setStudentEmailId(studentFromFeed.studentEmailId());
    erpStudent.setGender(genderEnum(studentFromFeed.gender()));
    erpStudent.setFatherName(studentFromFeed.fatherName());
    erpStudent.setFatherMobile(studentFromFeed.fatherMobile());
    erpStudent.setFatherEmail(studentFromFeed.fatherEmail());
    erpStudent.setMotherName(studentFromFeed.motherName());
    erpStudent.setMotherMobile(studentFromFeed.motherMobile());
    erpStudent.setMotherEmail(studentFromFeed.motherEmail());
    erpStudent.setClassRollNum(studentFromFeed.classRollNum());
    erpStudent.setStudentAdmissionNo(studentFromFeed.studentAdmissionNo());
    erpStudent.setDob(studentFromFeed.dob());
    erpStudent.setStudentCode(constructStudentCode(studentFromFeed));
    erpStudent.setSchoolId(schoolId);
    erpStudent.setGradeSlug(erpUtil.getGradeSlug(studentFromFeed.masterClassName().toUpperCase()));
    erpStudent.setClassName(studentFromFeed.className());
    erpStudent.setOrgSlug(orgSlug);
    erpStudent.setMasterClassName(studentFromFeed.masterClassName());
    return erpStudent;
  }

  private ErpFee saveFeeStudent(
      ErpFee erpFee, String schoolId, Fee studentFromFeed, String orgSlug) {
    erpFee.setStudentName(studentFromFeed.getStudentName());
    erpFee.setAdmissionNo(studentFromFeed.getAdmissionNo());
    erpFee.setRollNo(studentFromFeed.getRollNo());
    erpFee.setClassName(studentFromFeed.getClassName());
    erpFee.setDueAmount(studentFromFeed.getDueAmount());
    erpFee.setStudentCode(constructFeeStudentCode(studentFromFeed));
    erpFee.setSchoolId(schoolId);
    erpFee.setOrgSlug(orgSlug);
    erpFee.setDob(studentFromFeed.getDob());
    return erpFee;
  }

  private String constructStudentCode(StudentDto.Student studentFromFeed) {
    return studentFromFeed.studentAdmissionNo()
        + "-"
        + erpJobService.trimDob(studentFromFeed.dob());
  }

  private String constructFeeStudentCode(Fee studentFromFeed) {
    return studentFromFeed.getAdmissionNo() + "-" + erpJobService.trimDob(studentFromFeed.getDob());
  }

  private String genderEnum(String gender) {
    return switch (gender) {
      case "M" -> "MALE";
      case "F" -> "FEMALE";
      default -> "OTHER";
    };
  }

  private Map<String, ErpStudent> getStudentByCodeMap(String schoolId, String orgSlug) {
    Map<String, ErpStudent> studentMap = new HashMap<>();
    for (ErpStudent erpStudent : erpStudentRepository.findBySchoolId(schoolId)) {
      studentMap.put(erpStudent.getStudentCode(), erpStudent);
    }
    return studentMap;
  }

  private Map<String, ErpFee> getFeeStudentByCodeMap(String schoolId) {
    Map<String, ErpFee> studentMap = new HashMap<>();
    for (ErpFee erpFee : erpFeeRepository.findBySchoolId(schoolId)) {
      studentMap.put(erpFee.getStudentCode(), erpFee);
    }
    return studentMap;
  }

  private List<StudentDto.Student> getNewStudents(String schoolId) {
    if (refreshFeed) {
      final List<StudentDto.Student> allStudents = getAllStudents(schoolId);
      log.info("Fetched {} students from ERP", allStudents.size());
      saveRawStudentData(allStudents, schoolId);
      log.info("Saved {} students to raw student repository", allStudents.size());
      return allStudents;
    }
    return transformToStudents(rawErpStudentRepository.findBySchoolId(schoolId));
  }

  private List<Fee> getNewStudentFee(String schoolId, String orgSlug) {
    if (refreshFeed) {
      final List<Fee> allFeeStudents = getAllFeeStudents(schoolId, orgSlug);
      log.info("Fetched {} students from ERP", allFeeStudents.size());
      saveRawFeeStudentData(allFeeStudents, schoolId);
      log.info("Saved {} students to raw student repository", allFeeStudents.size());
      return allFeeStudents;
    }
    return transformToFeeStudents(rawErpFeeRepository.findBySchoolId(schoolId), orgSlug);
  }

  private List<StudentDto.Student> transformToStudents(List<RawErpStudent> bySchoolId) {
    return bySchoolId.stream()
        .map(
            rawStudent ->
                StudentDto.Student.builder()
                    .firstName(rawStudent.getFirstName())
                    .middleName(rawStudent.getMiddleName())
                    .lastName(rawStudent.getLastName())
                    .fullName(rawStudent.getFullName())
                    .studentMobile(rawStudent.getStudentMobile())
                    .studentEmailId(rawStudent.getStudentEmail())
                    .fatherName(rawStudent.getFatherName())
                    .motherName(rawStudent.getMotherName())
                    .fatherMobile(rawStudent.getFatherMobile())
                    .motherMobile(rawStudent.getMotherMobile())
                    .fatherEmail(rawStudent.getFatherEmail())
                    .motherEmail(rawStudent.getMotherEmail())
                    .fatherOccupation(rawStudent.getFatherOccupation())
                    .motherOccupation(rawStudent.getMotherOccupation())
                    .studentAdmissionNo(rawStudent.getStudentAdmissionNo())
                    .classId(rawStudent.getClassId())
                    .academicYear(rawStudent.getAcademicYear())
                    .gender(rawStudent.getGender())
                    .dob(rawStudent.getDob())
                    .classRollNum(rawStudent.getClassRollNum())
                    .dateOfAdmission(rawStudent.getDateOfAdmission())
                    .className(rawStudent.getClassName())
                    .masterClassId(rawStudent.getMasterClassId())
                    .masterClassName(rawStudent.getMasterClassName())
                    .build())
        .toList();
  }

  private List<Fee> transformToFeeStudents(List<RawErpFee> bySchoolId, String orgSlug) {
    List<String> admissionNos =
        bySchoolId.stream().map(RawErpFee::getAdmissionNo).collect(Collectors.toList());
    List<ErpStudent> erpStudents =
        erpStudentRepository.findByStudentAdmissionNoInAndOrgSlug(admissionNos, orgSlug);
    Map<String, ErpStudent> studentMap =
        erpStudents.stream()
            .collect(Collectors.toMap(ErpStudent::getStudentAdmissionNo, Function.identity()));
    return bySchoolId.stream()
        .filter(rawStudent -> studentMap.containsKey(rawStudent.getAdmissionNo()))
        .map(
            rawStudent -> {
              ErpStudent matchedStudent = studentMap.get(rawStudent.getAdmissionNo());
              return Fee.builder()
                  .studentName(rawStudent.getStudentName())
                  .admissionNo(rawStudent.getAdmissionNo())
                  .rollNo(rawStudent.getRollNo())
                  .className(rawStudent.getClassName())
                  .dueAmount(rawStudent.getDueAmount())
                  .dob(matchedStudent.getDob())
                  .orgSlug(orgSlug)
                  .build();
            })
        .toList();
  }

  private void saveRawStudentData(List<StudentDto.Student> allStudents, String schoolId) {
    rawErpStudentRepository.deleteAllBySchoolId(schoolId);
    rawErpStudentRepository.saveAll(transformToRawErpStudents(allStudents, schoolId));
  }

  private void saveRawFeeStudentData(List<Fee> allStudents, String schoolId) {
    rawErpFeeRepository.deleteAllBySchoolId(schoolId);
    rawErpFeeRepository.saveAll(transformToRawErpFeeStudents(allStudents, schoolId));
  }

  private List<RawErpStudent> transformToRawErpStudents(
      List<StudentDto.Student> allStudents, String schoolId) {
    return allStudents.stream()
        .map(
            student -> {
              RawErpStudent rawStudent = new RawErpStudent();
              rawStudent.setSchoolId(schoolId);
              rawStudent.setOrgSlug(erpUtil.getOrgSlugForSchoolId(schoolId));
              rawStudent.setFirstName(student.firstName());
              rawStudent.setMiddleName(student.middleName());
              rawStudent.setLastName(student.lastName());
              rawStudent.setFullName(student.fullName());
              rawStudent.setStudentMobile(student.studentMobile());
              rawStudent.setStudentEmail(student.studentEmailId());
              rawStudent.setFatherName(student.fatherName());
              rawStudent.setMotherName(student.motherName());
              rawStudent.setFatherMobile(student.fatherMobile());
              rawStudent.setMotherMobile(student.motherMobile());
              rawStudent.setFatherEmail(student.fatherEmail());
              rawStudent.setMotherEmail(student.motherEmail());
              rawStudent.setFatherOccupation(student.fatherOccupation());
              rawStudent.setMotherOccupation(student.motherOccupation());
              rawStudent.setStudentAdmissionNo(student.studentAdmissionNo());
              rawStudent.setClassId(student.classId());
              rawStudent.setAcademicYear(student.academicYear());
              rawStudent.setGender(student.gender());
              rawStudent.setDob(student.dob());
              rawStudent.setClassRollNum(student.classRollNum());
              rawStudent.setDateOfAdmission(student.dateOfAdmission());
              rawStudent.setClassName(student.className());
              rawStudent.setMasterClassId(student.masterClassId());
              rawStudent.setMasterClassName(student.masterClassName());
              return rawStudent;
            })
        .toList();
  }

  private List<RawErpFee> transformToRawErpFeeStudents(List<Fee> allStudents, String schoolId) {
    return allStudents.stream()
        .map(
            student -> {
              RawErpFee rawFeeStudent = new RawErpFee();
              rawFeeStudent.setSchoolId(schoolId);
              rawFeeStudent.setOrgSlug(erpUtil.getOrgSlugForStudentSchoolId(schoolId));
              rawFeeStudent.setStudentName(student.getStudentName());
              rawFeeStudent.setAdmissionNo(student.getAdmissionNo());
              rawFeeStudent.setRollNo(student.getRollNo());
              rawFeeStudent.setClassName(student.getClassName());
              rawFeeStudent.setDueAmount(student.getDueAmount());
              return rawFeeStudent;
            })
        .toList();
  }

  private List<StudentDto.Student> getAllStudents(String schoolId) {
    var uriComponent = UriComponentsBuilder.fromUriString(erpStudentUrl).build().encode().toUri();

    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);

    StudentDto.StudentRequest request =
        StudentDto.StudentRequest.builder().key(schoolId).academicYear(12).build();
    HttpEntity<String> httpEntity = new HttpEntity<>(jsonUtil.toJson(request), headers);

    ResponseEntity<String> response =
        restTemplate.exchange(uriComponent, HttpMethod.POST, httpEntity, String.class);

    try {
      ObjectMapper mapper = new ObjectMapper();
      mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

      StudentDto.StudentResponse studentData =
          mapper.readValue(response.getBody(), StudentDto.StudentResponse.class);
      if (studentData == null || !studentData.status().equalsIgnoreCase("success")) {
        log.error("Failed to fetch student data from ERP");
        return new ArrayList<>();
      }
      log.info("Fetching {} students from ERP", studentData.data().size());
      return studentData.data();
    } catch (JsonProcessingException e) {
      throw new RuntimeException(e);
    }
  }

  private List<Fee> getAllFeeStudents(String schoolId, String orgSlug) {
    String baseUrl = feeOrgConfigProperties.getOrgSlug().get(orgSlug);

    if (baseUrl == null || baseUrl.isEmpty()) {
      log.error("No ERP URL configured for orgSlug: {}", orgSlug);
      return Collections.emptyList();
    }

    String finalUrl = baseUrl + "&school_id=" + schoolId;
    URI uri = URI.create(finalUrl);

    HttpHeaders headers = new HttpHeaders();
    HttpEntity<?> httpEntity = new HttpEntity<>(headers);
    ResponseEntity<byte[]> response =
        restTemplate.exchange(uri, HttpMethod.GET, httpEntity, byte[].class);
    byte[] excelData = response.getBody();
    if (excelData == null || excelData.length == 0) {
      log.error("No data fetched from the endpoint");
      return new ArrayList<>();
    }
    try {
      Path debugPath = Paths.get("/tmp/debug.xlsx");
      Files.write(debugPath, excelData);
      log.info("Saved Excel file to: {}", debugPath.toAbsolutePath());
    } catch (Exception e) {
      log.error("Failed to save Excel for debugging: {}", e.getMessage());
    }
    return excelReader.extractStudentFeeData(excelData);
  }
}
